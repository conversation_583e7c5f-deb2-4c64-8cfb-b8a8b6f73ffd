-- 3-a  Table
create extension if not exists http;
create table if not exists qa (
  id uuid primary key default uuid_generate_v4(),
  question text not null,
  answer   text,
  status   text default 'pending',
  inserted_at timestamptz default now()
);

-- 3-b  Trigger wrapper
create or replace function call_generate_answer()
returns trigger language plpgsql as $$
begin
  perform http_post(
    url := 'https://bcfikfpgrasrcvvomehx.functions.supabase.co/bright-action',
    headers := json_build_object('Content-Type','application/json')::jsonb,
    body := json_build_object('id', new.id, 'question', new.question)::text
  );
  return new;
end;
$$;

drop trigger if exists trg_generate_answer on qa;
create trigger trg_generate_answer
after insert on qa
for each row execute procedure call_generate_answer();
