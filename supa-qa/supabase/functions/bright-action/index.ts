import { serve } from "https://deno.land/std@0.224.0/http/server.ts";
import { OpenAI } from "https://deno.land/x/openai@1.4.2/mod.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const openai = new OpenAI({ apiKey: Deno.env.get("OPENAI_API_KEY")! });
const supabase = createClient(
  Deno.env.get("SUPABASE_URL")!,
  Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!
);

serve(async (req) => {
  const { id, question } = await req.json();
  if (!id || !question) return new Response("bad payload", { status: 400 });

  try {
    const chat = await openai.createChatCompletion({
      model: "gpt-4o",
      messages: [
        { role: "system", content: "You are a helpful assistant." },
        { role: "user", content: question },
      ],
    });
    const answer = chat.choices?.[0]?.message?.content ?? "";

    const { error } = await supabase
      .from("qa")
      .update({ answer, status: "ready" })
      .eq("id", id);
    if (error) throw error;

    return new Response("ok");
  } catch (err) {
    await supabase.from("qa").update({ status: "error" }).eq("id", id);
    return new Response("fail", { status: 500 });
  }
});
